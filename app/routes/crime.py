from fastapi import APIRouter, Depends, Query, Header, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from app.config.database import get_db
from app.controllers.crime_controller import CrimeController
from app.schemas.crime_data import CreateCrimeRequest
from app.schemas.response import APIResponse
from app.utils.security import verify_token

def get_current_user_id(authorization: str = Header(None)):
    """Get current user_id from JWT token in Authorization header"""
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header missing or invalid"
        )

    token = authorization.split(" ")[1]
    user_id = verify_token(token)
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token"
        )

    return user_id

router = APIRouter(prefix="/crime", tags=["Crime Data"])

@router.get("/search", response_model=APIResponse)
async def search_crimes(
    from_date: Optional[str] = Query(None, description="Start date (ISO format: YYYY-MM-DDTHH:MM:SS)"),
    to_date: Optional[str] = Query(None, description="End date (ISO format: YYYY-MM-DDTHH:MM:SS)"),
    crime_type: Optional[str] = Query(None, description="Type of crime"),
    province: Optional[str] = Query(None, description="Province name"),
    city: Optional[str] = Query(None, description="City name"),
    area: Optional[str] = Query(None, description="Area/locality name"),
    status: Optional[str] = Query(None, description="Crime status: reported, investigating, resolved, closed"),
    severity: Optional[str] = Query(None, description="Crime severity: low, medium, high, critical"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Records per page"),
    db: Session = Depends(get_db)
):
    """
    Search crimes with various filters and pagination
    
    **Query Parameters:**
    - **from_date**: Start date for search (ISO format: 2024-01-01T00:00:00)
    - **to_date**: End date for search (ISO format: 2024-12-31T23:59:59)
    - **crime_type**: Type of crime (e.g., "theft", "assault", "burglary")
    - **province**: Province name (e.g., "Punjab", "Sindh")
    - **city**: City name (e.g., "Karachi", "Lahore")
    - **area**: Area/locality name (e.g., "DHA", "Gulshan")
    - **status**: Crime status (reported, investigating, resolved, closed)
    - **severity**: Crime severity (low, medium, high, critical)
    - **page**: Page number for pagination (default: 1)
    - **limit**: Number of records per page (default: 10, max: 100)
    
    **Example Usage:**
    ```
    GET /crime/search?crime_type=theft&city=Karachi&from_date=2024-01-01T00:00:00&to_date=2024-12-31T23:59:59&page=1&limit=20
    ```
    """
    return CrimeController.search_crimes(
        from_date=from_date,
        to_date=to_date,
        crime_type=crime_type,
        province=province,
        city=city,
        area=area,
        status=status,
        severity=severity,
        page=page,
        limit=limit,
        db=db
    )

@router.post("/create", response_model=APIResponse)
async def create_crime(
    request: CreateCrimeRequest,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """
    Create a new crime record
    
    **Required Authentication:** JWT token in Authorization header
    
    **Request Body:**
    ```json
    {
        "crime_type": "theft",
        "province": "Punjab",
        "city": "Lahore",
        "area": "DHA Phase 5",
        "description": "Mobile phone stolen from car",
        "latitude": 31.5204,
        "longitude": 74.3587,
        "incident_date": "2024-01-15T14:30:00",
        "status": "reported",
        "severity": "medium"
    }
    ```
    """
    return CrimeController.create_crime(request, db)

@router.get("/{crime_id}", response_model=APIResponse)
async def get_crime(
    crime_id: int,
    db: Session = Depends(get_db)
):
    """
    Get crime record by ID
    
    **Path Parameters:**
    - **crime_id**: Unique identifier of the crime record
    """
    return CrimeController.get_crime(crime_id, db)

@router.get("/statistics/overview", response_model=APIResponse)
async def get_crime_statistics(
    db: Session = Depends(get_db)
):
    """
    Get crime statistics overview
    
    **Returns:**
    - Total crime count
    - Crime types distribution
    - Status distribution
    - Severity distribution
    - Province distribution
    """
    return CrimeController.get_crime_statistics(db)
